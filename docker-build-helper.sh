#!/bin/bash

### Script helper để tối ưu Docker build
### Sử dụng: ./docker-build-helper.sh [command]
### Commands: setup, clean, analyze, optimize

set -e

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Hàm hiển thị thông tin
info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Hàm setup Docker buildx
setup_buildx() {
    info "Thiết lập Docker buildx..."
    
    # Kiểm tra xem buildx đã được cài đặt chưa
    if ! docker buildx version &> /dev/null; then
        error "Docker buildx chưa được cài đặt. <PERSON><PERSON> lòng cập nhật Docker lên phiên bản mới nhất."
        exit 1
    fi
    
    # Tạo builder instance mới nếu chưa có
    if ! docker buildx ls | grep -q "chatwoot-builder"; then
        info "Tạo builder instance mới..."
        docker buildx create --name chatwoot-builder --driver docker-container --bootstrap
        success "Đã tạo builder instance: chatwoot-builder"
    else
        info "Builder instance đã tồn tại: chatwoot-builder"
    fi
    
    # Sử dụng builder instance
    docker buildx use chatwoot-builder
    success "Đã thiết lập Docker buildx"
}

# Hàm dọn dẹp Docker
clean_docker() {
    info "Dọn dẹp Docker cache và images cũ..."
    
    # Dọn dẹp build cache
    docker builder prune -f --filter until=24h
    
    # Dọn dẹp images không sử dụng
    docker image prune -f --filter until=24h
    
    # Dọn dẹp containers đã dừng
    docker container prune -f
    
    # Dọn dẹp volumes không sử dụng
    docker volume prune -f
    
    # Dọn dẹp networks không sử dụng
    docker network prune -f
    
    success "Đã dọn dẹp Docker"
}

# Hàm phân tích Docker build
analyze_build() {
    info "Phân tích Docker build performance..."
    
    # Kiểm tra kích thước context
    CONTEXT_SIZE=$(du -sh . | cut -f1)
    info "Kích thước build context: $CONTEXT_SIZE"
    
    # Kiểm tra .dockerignore
    if [ -f ".dockerignore" ]; then
        IGNORED_FILES=$(find . -name "*" | wc -l)
        info "Số file trong context: $IGNORED_FILES"
        success ".dockerignore đã được cấu hình"
    else
        warning ".dockerignore không tồn tại - có thể làm chậm build"
    fi
    
    # Kiểm tra cache images
    info "Kiểm tra cache images..."
    docker images | grep "thotran113/chatwoot-interactive" | head -5
    
    # Hiển thị thông tin Docker
    info "Thông tin Docker:"
    docker version --format "Client: {{.Client.Version}}, Server: {{.Server.Version}}"
    docker system df
}

# Hàm tối ưu build
optimize_build() {
    info "Tối ưu cấu hình build..."
    
    # Thiết lập Docker daemon config
    DOCKER_CONFIG_DIR="/etc/docker"
    DOCKER_CONFIG_FILE="$DOCKER_CONFIG_DIR/daemon.json"
    
    if [ -w "$DOCKER_CONFIG_DIR" ] || [ "$(id -u)" -eq 0 ]; then
        info "Cấu hình Docker daemon..."
        
        # Backup config cũ nếu có
        if [ -f "$DOCKER_CONFIG_FILE" ]; then
            cp "$DOCKER_CONFIG_FILE" "$DOCKER_CONFIG_FILE.backup"
        fi
        
        # Tạo config tối ưu
        cat > "$DOCKER_CONFIG_FILE" << EOF
{
  "builder": {
    "gc": {
      "enabled": true,
      "defaultKeepStorage": "20GB"
    }
  },
  "experimental": true,
  "features": {
    "buildkit": true
  }
}
EOF
        success "Đã cấu hình Docker daemon"
        warning "Cần restart Docker daemon để áp dụng cấu hình mới"
    else
        warning "Không có quyền ghi vào $DOCKER_CONFIG_DIR"
        info "Chạy với sudo hoặc cấu hình thủ công"
    fi
    
    # Thiết lập environment variables
    info "Thiết lập environment variables..."
    cat >> ~/.bashrc << 'EOF'

# Docker BuildKit optimization
export DOCKER_BUILDKIT=1
export BUILDKIT_PROGRESS=plain
export DOCKER_CLI_EXPERIMENTAL=enabled
EOF
    
    success "Đã thêm environment variables vào ~/.bashrc"
    info "Chạy 'source ~/.bashrc' để áp dụng"
}

# Hàm hiển thị thống kê build
show_build_stats() {
    info "Thống kê build performance..."
    
    if [ -f ".last_build_time" ]; then
        LAST_BUILD_TIME=$(cat .last_build_time)
        info "Thời gian build lần trước: $LAST_BUILD_TIME giây"
    fi
    
    # Hiển thị kích thước images
    info "Kích thước images:"
    docker images thotran113/chatwoot-interactive --format "table {{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | head -10
}

# Main function
main() {
    case "${1:-help}" in
        "setup")
            setup_buildx
            ;;
        "clean")
            clean_docker
            ;;
        "analyze")
            analyze_build
            ;;
        "optimize")
            optimize_build
            ;;
        "stats")
            show_build_stats
            ;;
        "all")
            setup_buildx
            clean_docker
            optimize_build
            analyze_build
            ;;
        "help"|*)
            echo "Docker Build Helper - Tối ưu Docker build cho Chatwoot"
            echo ""
            echo "Sử dụng: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  setup     - Thiết lập Docker buildx"
            echo "  clean     - Dọn dẹp Docker cache và images cũ"
            echo "  analyze   - Phân tích build performance"
            echo "  optimize  - Tối ưu cấu hình build"
            echo "  stats     - Hiển thị thống kê build"
            echo "  all       - Chạy tất cả các lệnh tối ưu"
            echo "  help      - Hiển thị trợ giúp này"
            echo ""
            echo "Ví dụ:"
            echo "  $0 setup    # Thiết lập buildx"
            echo "  $0 clean    # Dọn dẹp cache"
            echo "  $0 all      # Tối ưu toàn bộ"
            ;;
    esac
}

main "$@"
