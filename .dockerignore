.bundle
.env
.env.*
docker-compose.*
docker/Dockerfile
docker/dockerfiles
log
storage
public/system
tmp
.codeclimate.yml
public/packs
node_modules
vendor/bundle
.DS_Store
*.swp
*~

# Thêm các file không cần thiết để giảm thời gian build
.git
.github
.gitignore
.rspec
.rubocop.yml
.ruby-version
.yarnrc
spec
test
doc
docs
README.md
CHANGELOG.md
LICENSE
*.md
*.log
*.sql
*.sqlite
*.sqlite3
*.csv
*.dump
*.tar.gz
*.zip
*.gz

# Build và development files
coverage
.nyc_output
.jest
.vscode
.idea
*.orig
*.rej
.sass-cache
.cache
.parcel-cache

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Build scripts (không cần trong container)
build-and-check*.sh
.last_version

# Docker files khác
docker-compose*.yml
Dockerfile*
!docker/Dockerfile.optimized
