#!/bin/bash

### Script benchmark để so sánh hiệu suất build
### Sử dụng: ./benchmark-build.sh [test_type]

set -e

# Màu sắc
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }

USERNAME="thotran113"
APP_NAME="chatwoot-interactive"
IMAGE_NAME="$USERNAME/$APP_NAME"

# Hàm benchmark build từ scratch
benchmark_clean_build() {
    info "🧪 Benchmark: Clean build (no cache)"
    
    # Dọn dẹp tất cả cache và images
    warning "Dọn dẹp tất cả cache và images..."
    docker system prune -af
    docker builder prune -af
    
    # Ghi lại thời gian
    START_TIME=$(date +%s)
    
    # Build với Dockerfile.optimized
    info "Building với Dockerfile.optimized..."
    docker build \
        --no-cache \
        -t "$IMAGE_NAME:benchmark-clean" \
        -f docker/Dockerfile.optimized \
        .
    
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    success "Clean build hoàn thành trong ${DURATION}s"
    echo "$DURATION" > .benchmark_clean_build
    
    return $DURATION
}

# Hàm benchmark build với cache
benchmark_cached_build() {
    info "🧪 Benchmark: Cached build"
    
    # Đảm bảo có base image để cache
    if ! docker image inspect "$IMAGE_NAME:benchmark-clean" &> /dev/null; then
        warning "Không tìm thấy base image, chạy clean build trước..."
        benchmark_clean_build
    fi
    
    # Tag image để làm cache
    docker tag "$IMAGE_NAME:benchmark-clean" "$IMAGE_NAME:latest"
    
    # Ghi lại thời gian
    START_TIME=$(date +%s)
    
    # Build với cache
    info "Building với cache..."
    docker build \
        --cache-from "$IMAGE_NAME:latest" \
        -t "$IMAGE_NAME:benchmark-cached" \
        -f docker/Dockerfile.optimized \
        .
    
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    success "Cached build hoàn thành trong ${DURATION}s"
    echo "$DURATION" > .benchmark_cached_build
    
    return $DURATION
}

# Hàm so sánh với Dockerfile gốc
benchmark_original_dockerfile() {
    info "🧪 Benchmark: Original Dockerfile"
    
    # Dọn dẹp cache
    docker builder prune -f
    
    # Ghi lại thời gian
    START_TIME=$(date +%s)
    
    # Build với Dockerfile gốc
    info "Building với Dockerfile gốc..."
    docker build \
        -t "$IMAGE_NAME:benchmark-original" \
        -f docker/Dockerfile \
        .
    
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    success "Original build hoàn thành trong ${DURATION}s"
    echo "$DURATION" > .benchmark_original_build
    
    return $DURATION
}

# Hàm hiển thị kết quả benchmark
show_benchmark_results() {
    info "📊 Kết quả Benchmark"
    echo "=================================="
    
    if [ -f ".benchmark_clean_build" ]; then
        CLEAN_TIME=$(cat .benchmark_clean_build)
        CLEAN_MIN=$((CLEAN_TIME / 60))
        CLEAN_SEC=$((CLEAN_TIME % 60))
        echo "🔥 Clean build (Optimized): ${CLEAN_MIN}m ${CLEAN_SEC}s (${CLEAN_TIME}s)"
    fi
    
    if [ -f ".benchmark_cached_build" ]; then
        CACHED_TIME=$(cat .benchmark_cached_build)
        CACHED_MIN=$((CACHED_TIME / 60))
        CACHED_SEC=$((CACHED_TIME % 60))
        echo "⚡ Cached build (Optimized): ${CACHED_MIN}m ${CACHED_SEC}s (${CACHED_TIME}s)"
    fi
    
    if [ -f ".benchmark_original_build" ]; then
        ORIGINAL_TIME=$(cat .benchmark_original_build)
        ORIGINAL_MIN=$((ORIGINAL_TIME / 60))
        ORIGINAL_SEC=$((ORIGINAL_TIME % 60))
        echo "🐌 Original build: ${ORIGINAL_MIN}m ${ORIGINAL_SEC}s (${ORIGINAL_TIME}s)"
    fi
    
    echo "=================================="
    
    # Tính toán cải thiện
    if [ -f ".benchmark_clean_build" ] && [ -f ".benchmark_original_build" ]; then
        IMPROVEMENT=$(( (ORIGINAL_TIME - CLEAN_TIME) * 100 / ORIGINAL_TIME ))
        if [ $IMPROVEMENT -gt 0 ]; then
            success "Cải thiện clean build: ${IMPROVEMENT}%"
        else
            warning "Clean build chậm hơn: $((0 - IMPROVEMENT))%"
        fi
    fi
    
    if [ -f ".benchmark_cached_build" ] && [ -f ".benchmark_clean_build" ]; then
        CACHE_IMPROVEMENT=$(( (CLEAN_TIME - CACHED_TIME) * 100 / CLEAN_TIME ))
        if [ $CACHE_IMPROVEMENT -gt 0 ]; then
            success "Cải thiện nhờ cache: ${CACHE_IMPROVEMENT}%"
        fi
    fi
}

# Hàm phân tích image size
analyze_image_sizes() {
    info "📏 Phân tích kích thước images"
    echo "=================================="
    
    for tag in benchmark-clean benchmark-cached benchmark-original latest; do
        if docker image inspect "$IMAGE_NAME:$tag" &> /dev/null; then
            SIZE=$(docker image inspect "$IMAGE_NAME:$tag" --format='{{.Size}}' | numfmt --to=iec)
            echo "$tag: $SIZE"
        fi
    done
    
    echo "=================================="
}

# Hàm dọn dẹp benchmark
cleanup_benchmark() {
    info "🧹 Dọn dẹp benchmark images"
    
    for tag in benchmark-clean benchmark-cached benchmark-original; do
        if docker image inspect "$IMAGE_NAME:$tag" &> /dev/null; then
            docker rmi "$IMAGE_NAME:$tag" || true
        fi
    done
    
    # Xóa file benchmark
    rm -f .benchmark_*
    
    success "Đã dọn dẹp benchmark"
}

# Hàm chạy full benchmark
run_full_benchmark() {
    info "🚀 Chạy full benchmark suite"
    
    echo "⚠️  Cảnh báo: Quá trình này sẽ mất nhiều thời gian và xóa cache hiện tại"
    read -p "Bạn có muốn tiếp tục? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "Hủy benchmark"
        exit 0
    fi
    
    # Chạy các benchmark
    benchmark_original_dockerfile
    benchmark_clean_build
    benchmark_cached_build
    
    # Hiển thị kết quả
    show_benchmark_results
    analyze_image_sizes
    
    # Hỏi có muốn dọn dẹp không
    echo
    read -p "Dọn dẹp benchmark images? (Y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        cleanup_benchmark
    fi
}

# Main function
main() {
    case "${1:-help}" in
        "clean")
            benchmark_clean_build
            ;;
        "cached")
            benchmark_cached_build
            ;;
        "original")
            benchmark_original_dockerfile
            ;;
        "results")
            show_benchmark_results
            ;;
        "sizes")
            analyze_image_sizes
            ;;
        "cleanup")
            cleanup_benchmark
            ;;
        "full")
            run_full_benchmark
            ;;
        "help"|*)
            echo "Docker Build Benchmark Tool"
            echo ""
            echo "Sử dụng: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  clean     - Benchmark clean build (no cache)"
            echo "  cached    - Benchmark cached build"
            echo "  original  - Benchmark original Dockerfile"
            echo "  results   - Hiển thị kết quả benchmark"
            echo "  sizes     - Phân tích kích thước images"
            echo "  cleanup   - Dọn dẹp benchmark images"
            echo "  full      - Chạy full benchmark suite"
            echo "  help      - Hiển thị trợ giúp này"
            echo ""
            echo "Ví dụ:"
            echo "  $0 full     # Chạy full benchmark"
            echo "  $0 results  # Xem kết quả"
            echo "  $0 cleanup  # Dọn dẹp"
            ;;
    esac
}

main "$@"
