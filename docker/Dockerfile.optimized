# pre-build stage - T<PERSON>i ưu cho cache layers
FROM node:23-alpine as node
FROM ruby:3.4.4-alpine3.21 AS pre-builder

# Thiết lập các ARG và ENV một lần để tối ưu cache
ARG NODE_VERSION="23.7.0"
ARG PNPM_VERSION="10.2.0"
ARG BUNDLE_WITHOUT="development:test"
ARG RAILS_SERVE_STATIC_FILES=true
ARG RAILS_ENV=production
ARG NODE_OPTIONS="--max-old-space-size=4096 --openssl-legacy-provider"

ENV NODE_VERSION=${NODE_VERSION} \
    PNPM_VERSION=${PNPM_VERSION} \
    BUNDLE_WITHOUT=${BUNDLE_WITHOUT} \
    BUNDLER_VERSION=2.5.11 \
    RAILS_SERVE_STATIC_FILES=${RAILS_SERVE_STATIC_FILES} \
    RAILS_ENV=${RAILS_ENV} \
    NODE_OPTIONS=${NODE_OPTIONS} \
    BUNDLE_PATH="/gems" \
    PNPM_HOME="/root/.local/share/pnpm" \
    PATH="/root/.local/share/pnpm:$PATH"

# Cài đặt system dependencies trong một layer để tối ưu cache
RUN --mount=type=cache,target=/var/cache/apk \
    apk update && apk add --no-cache \
    openssl \
    tar \
    build-base \
    tzdata \
    postgresql-dev \
    postgresql-client \
    git \
    curl \
    xz \
    vips \
    musl \
    ruby-full \
    ruby-dev \
    gcc \
    make \
    musl-dev \
    openssl-dev \
    g++ \
    linux-headers \
    && mkdir -p /var/app \
    && gem install bundler

# Sao chép Node.js từ image node và cài đặt pnpm trong một layer
COPY --from=node /usr/local/bin/node /usr/local/bin/
COPY --from=node /usr/local/lib/node_modules /usr/local/lib/node_modules
RUN ln -s /usr/local/lib/node_modules/npm/bin/npm-cli.js /usr/local/bin/npm \
    && ln -s /usr/local/lib/node_modules/npm/bin/npx-cli.js /usr/local/bin/npx \
    && npm install -g pnpm@${PNPM_VERSION} \
    && pnpm --version

WORKDIR /app

# Sao chép và cài đặt Ruby dependencies với cache mount
COPY Gemfile Gemfile.lock ./
RUN bundle config set --local force_ruby_platform true

# Cài đặt Ruby gems với cache mount để tối ưu
RUN --mount=type=cache,target=/usr/local/bundle/cache \
    if [ "$RAILS_ENV" = "production" ]; then \
        bundle config set without 'development test' && bundle install -j$(nproc) -r 3; \
    else \
        bundle install -j$(nproc) -r 3; \
    fi

# Sao chép và cài đặt Node.js dependencies với cache mount
COPY package.json pnpm-lock.yaml ./
RUN --mount=type=cache,target=/root/.local/share/pnpm/store \
    --mount=type=cache,target=/root/.cache/pnpm \
    pnpm i --frozen-lockfile --prefer-offline

# Sao chép source code
COPY . /app

# Tạo thư mục log và lưu git sha
RUN mkdir -p /app/log \
    && (git rev-parse HEAD > /app/.git_sha 2>/dev/null || echo "unknown" > /app/.git_sha)

# Precompile assets với cache mount và tối ưu
RUN --mount=type=cache,target=/app/tmp/cache \
    --mount=type=cache,target=/app/public/vite \
    if [ "$RAILS_ENV" = "production" ]; then \
        SECRET_KEY_BASE=precompile_placeholder RAILS_LOG_TO_STDOUT=enabled \
        bundle exec rake assets:precompile \
        && rm -rf spec node_modules; \
    fi

# Dọn dẹp để giảm kích thước image trong một layer
RUN rm -rf /gems/ruby/3.4.0/cache/*.gem \
    && find /gems/ruby/3.4.0/gems/ \( -name "*.c" -o -name "*.o" \) -delete \
    && rm -rf .git \
    && rm -f .gitignore \
    && rm -rf /var/cache/apk/* \
    && rm -rf /tmp/*

# final build stage - Runtime image tối ưu
FROM ruby:3.4.4-alpine3.21

# Thiết lập tất cả ENV trong một layer
ARG NODE_VERSION="23.7.0"
ARG PNPM_VERSION="10.2.0"
ARG BUNDLE_WITHOUT="development:test"
ARG EXECJS_RUNTIME="Disabled"
ARG RAILS_SERVE_STATIC_FILES=true
ARG BUNDLE_FORCE_RUBY_PLATFORM=1
ARG RAILS_ENV=production

ENV NODE_VERSION=${NODE_VERSION} \
    PNPM_VERSION=${PNPM_VERSION} \
    BUNDLE_WITHOUT=${BUNDLE_WITHOUT} \
    BUNDLER_VERSION=2.5.11 \
    EXECJS_RUNTIME=${EXECJS_RUNTIME} \
    RAILS_SERVE_STATIC_FILES=${RAILS_SERVE_STATIC_FILES} \
    BUNDLE_FORCE_RUBY_PLATFORM=${BUNDLE_FORCE_RUBY_PLATFORM} \
    RAILS_ENV=${RAILS_ENV} \
    BUNDLE_PATH="/gems"

# Cài đặt runtime dependencies trong một layer với cache
RUN --mount=type=cache,target=/var/cache/apk \
    apk update && apk add --no-cache \
    openssl \
    tzdata \
    postgresql-client \
    imagemagick \
    git \
    vips \
    && gem install bundler

# Sao chép Node.js từ image node (chỉ runtime files)
COPY --from=node /usr/local/bin/node /usr/local/bin/
COPY --from=node /usr/local/lib/node_modules /usr/local/lib/node_modules

# Thiết lập Node.js tools chỉ khi cần thiết
RUN if [ "$RAILS_ENV" != "production" ]; then \
        ln -s /usr/local/lib/node_modules/npm/bin/npm-cli.js /usr/local/bin/npm \
        && ln -s /usr/local/lib/node_modules/npm/bin/npx-cli.js /usr/local/bin/npx \
        && npm install -g pnpm@${PNPM_VERSION}; \
    fi

# Sao chép artifacts từ pre-builder stage
COPY --from=pre-builder /gems/ /gems/
COPY --from=pre-builder /app /app

WORKDIR /app

EXPOSE 3000

# Sử dụng CMD thay vì ENTRYPOINT để dễ dàng ghi đè khi cần
CMD ["bundle", "exec", "rails", "server", "-b", "0.0.0.0"]
